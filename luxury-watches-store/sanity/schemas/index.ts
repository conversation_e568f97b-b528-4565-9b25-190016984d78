import { product } from './product'
import { productVariant } from './product-variant'
import { jewelry } from './jewelry'
import { brand } from './brand'
import { collection } from './collection'
import { category } from './category'
import { review } from './review'
import { blogPost } from './blog-post'
import { page } from './page'
import { landingPage } from './landing-page'
import { lookbook } from './lookbook'
import { siteSettings } from './site-settings'
import { siteSettingsEnhanced } from './site-settings-enhanced'

export const schemaTypes = [
  // Product related schemas
  product,
  productVariant,
  jewelry,
  brand,
  collection,
  category,
  review,

  // Content schemas
  blogPost,
  page,
  landingPage,
  lookbook,

  // Settings
  siteSettings,
  siteSettingsEnhanced,
]
