import { StructureBuilder } from 'sanity/structure'
import { 
  CogIcon, 
  DocumentIcon, 
  ImageIcon, 
  TagIcon, 
  UserIcon,
  ShoppingBagIcon,
  StarIcon,
  BookOpenIcon,
  PresentationChartLineIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline'

export const structure = (S: StructureBuilder) =>
  S.list()
    .title('Atlas Luxury CMS')
    .items([
      // Site Settings (Singleton)
      S.listItem()
        .title('Site Settings')
        .icon(CogIcon)
        .child(
          S.document()
            .schemaType('siteSettings')
            .documentId('siteSettings')
        ),

      S.divider(),

      // Products Section
      S.listItem()
        .title('Products')
        .icon(ShoppingBagIcon)
        .child(
          S.list()
            .title('Product Management')
            .items([
              S.listItem()
                .title('All Products')
                .child(
                  S.documentTypeList('product')
                    .title('All Products')
                    .filter('_type == "product"')
                    .defaultOrdering([{ field: '_createdAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('Watches')
                .child(
                  S.documentTypeList('product')
                    .title('Watches')
                    .filter('_type == "product" && productType == "watch"')
                ),
              S.listItem()
                .title('Jewelry')
                .child(
                  S.documentTypeList('product')
                    .title('Jewelry')
                    .filter('_type == "product" && productType == "jewelry"')
                ),
              S.listItem()
                .title('Featured Products')
                .child(
                  S.documentTypeList('product')
                    .title('Featured Products')
                    .filter('_type == "product" && isFeatured == true')
                ),
              S.listItem()
                .title('Out of Stock')
                .child(
                  S.documentTypeList('product')
                    .title('Out of Stock')
                    .filter('_type == "product" && inventory.stock <= 0')
                ),
              S.divider(),
              S.listItem()
                .title('Product Variants')
                .child(S.documentTypeList('productVariant')),
            ])
        ),

      // Catalog Organization
      S.listItem()
        .title('Catalog')
        .icon(TagIcon)
        .child(
          S.list()
            .title('Catalog Organization')
            .items([
              S.listItem()
                .title('Brands')
                .child(S.documentTypeList('brand')),
              S.listItem()
                .title('Collections')
                .child(S.documentTypeList('collection')),
              S.listItem()
                .title('Categories')
                .child(S.documentTypeList('category')),
            ])
        ),

      S.divider(),

      // Content Section
      S.listItem()
        .title('Content')
        .icon(DocumentIcon)
        .child(
          S.list()
            .title('Content Management')
            .items([
              S.listItem()
                .title('Landing Pages')
                .child(
                  S.documentTypeList('landingPage')
                    .title('Landing Pages')
                    .filter('_type == "landingPage"')
                ),
              S.listItem()
                .title('Custom Pages')
                .child(
                  S.documentTypeList('page')
                    .title('Custom Pages')
                    .filter('_type == "page"')
                ),
              S.listItem()
                .title('Blog Posts')
                .child(
                  S.documentTypeList('blogPost')
                    .title('Blog Posts')
                    .filter('_type == "blogPost"')
                ),
              S.listItem()
                .title('Lookbooks')
                .child(
                  S.documentTypeList('lookbook')
                    .title('Lookbooks')
                    .filter('_type == "lookbook"')
                ),
            ])
        ),

      // Media Section
      S.listItem()
        .title('Media Library')
        .icon(ImageIcon)
        .child(
          S.list()
            .title('Media Management')
            .items([
              S.listItem()
                .title('All Images')
                .child(
                  S.documentTypeList('sanity.imageAsset')
                    .title('All Images')
                ),
              S.listItem()
                .title('Product Images')
                .child(
                  S.documentTypeList('sanity.imageAsset')
                    .title('Product Images')
                    .filter('_type == "sanity.imageAsset" && references(*[_type == "product"]._id)')
                ),
              S.listItem()
                .title('Brand Assets')
                .child(
                  S.documentTypeList('sanity.imageAsset')
                    .title('Brand Assets')
                    .filter('_type == "sanity.imageAsset" && references(*[_type == "brand"]._id)')
                ),
            ])
        ),

      S.divider(),

      // Customer Engagement
      S.listItem()
        .title('Customer Engagement')
        .icon(UserIcon)
        .child(
          S.list()
            .title('Customer Content')
            .items([
              S.listItem()
                .title('Reviews')
                .child(S.documentTypeList('review')),
              S.listItem()
                .title('Testimonials')
                .child(
                  S.documentTypeList('review')
                    .title('Testimonials')
                    .filter('_type == "review" && isFeatured == true')
                ),
            ])
        ),

      // Analytics & Reports
      S.listItem()
        .title('Analytics')
        .icon(PresentationChartLineIcon)
        .child(
          S.list()
            .title('Analytics & Reports')
            .items([
              S.listItem()
                .title('Popular Products')
                .child(
                  S.documentTypeList('product')
                    .title('Popular Products')
                    .filter('_type == "product"')
                    .defaultOrdering([{ field: 'views', direction: 'desc' }])
                ),
              S.listItem()
                .title('Recent Activity')
                .child(
                  S.documentTypeList('*')
                    .title('Recent Activity')
                    .filter('_type in ["product", "blogPost", "landingPage", "lookbook"]')
                    .defaultOrdering([{ field: '_updatedAt', direction: 'desc' }])
                ),
            ])
        ),

      S.divider(),

      // Localization
      S.listItem()
        .title('Localization')
        .icon(GlobeAltIcon)
        .child(
          S.list()
            .title('Localization Management')
            .items([
              S.listItem()
                .title('English Content')
                .child(
                  S.documentTypeList('*')
                    .title('English Content')
                    .filter('_type in ["product", "brand", "collection", "blogPost"] && language == "en"')
                ),
              S.listItem()
                .title('German Content')
                .child(
                  S.documentTypeList('*')
                    .title('German Content')
                    .filter('_type in ["product", "brand", "collection", "blogPost"] && language == "de"')
                ),
              S.listItem()
                .title('French Content')
                .child(
                  S.documentTypeList('*')
                    .title('French Content')
                    .filter('_type in ["product", "brand", "collection", "blogPost"] && language == "fr"')
                ),
              S.listItem()
                .title('Translation Status')
                .child(
                  S.documentTypeList('*')
                    .title('Needs Translation')
                    .filter('_type in ["product", "brand", "collection", "blogPost"] && !defined(translations)')
                ),
            ])
        ),
    ])

export default structure
